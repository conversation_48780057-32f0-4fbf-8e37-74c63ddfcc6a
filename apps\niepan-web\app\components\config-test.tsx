'use client';

import { useConfig, getConfig, isConfigLoaded } from '@/hooks/useConfig';

export default function ConfigTest() {
  const { globalConfig, serverConfig } = useConfig();
  const configLoaded = isConfigLoaded();

  return (
    <div className="p-4 border rounded-lg bg-gray-50">
      <h3 className="text-lg font-semibold mb-4">配置加载测试</h3>

      <div className="mb-4">
        <p className="font-medium">
          配置加载状态:
          <span
            className={`ml-2 px-2 py-1 rounded text-sm ${
              configLoaded ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}
          >
            {configLoaded ? '已加载' : '未加载'}
          </span>
        </p>
      </div>

      <div className="mb-4">
        <h4 className="font-medium mb-2">全局配置 (_aa_global_config):</h4>
        <pre className="bg-white p-2 rounded border text-sm overflow-auto">
          {JSON.stringify(globalConfig, null, 2)}
        </pre>
      </div>

      <div className="mb-4">
        <h4 className="font-medium mb-2">服务器配置 (_server_config):</h4>
        <pre className="bg-white p-2 rounded border text-sm overflow-auto">
          {JSON.stringify(serverConfig, null, 2)}
        </pre>
      </div>

      <div className="mb-4">
        <h4 className="font-medium mb-2">使用 getConfig 函数获取特定配置:</h4>
        <ul className="space-y-1 text-sm">
          <li>test: {getConfig('test', '未找到')}</li>
          <li>apiBaseUrl: {getConfig('apiBaseUrl', '未找到')}</li>
          <li>environment: {getConfig('environment', '未找到')}</li>
          <li>loginRedirectUrl: {getConfig('loginRedirectUrl', '未找到')}</li>
        </ul>
      </div>

      <div>
        <h4 className="font-medium mb-2">Window对象检查:</h4>
        <ul className="space-y-1 text-sm">
          <li>
            window._aa_global_config 存在:{' '}
            {typeof window !== 'undefined' && window._aa_global_config ? '是' : '否'}
          </li>
          <li>
            window._server_config 存在:{' '}
            {typeof window !== 'undefined' && window._server_config ? '是' : '否'}
          </li>
        </ul>
      </div>
    </div>
  );
}
