# Next.js 动态配置系统

本文档介绍如何在Next.js项目中实现类似Vue项目的动态配置加载功能。

## 功能概述

这个动态配置系统允许你：
1. 在运行时动态加载配置文件
2. 无需重新构建应用即可更新配置
3. 支持多环境配置管理
4. 提供类型安全的配置访问

## 实现原理

### 1. 脚本注入
在 `app/layout.tsx` 中使用 Next.js 的 `Script` 组件注入动态配置脚本：

```tsx
<Script
  id="dynamic-config-loader"
  strategy="beforeInteractive"
  dangerouslySetInnerHTML={{
    __html: `
      document.write(
        '<s' +
          "cript type='text/javascript' src='/static-config/config.js?" +
          Math.random() +
          "'></scr" +
          'ipt>'
      );
    `,
  }}
/>
```

### 2. 配置文件
配置文件位于 `public/static-config/config.js`，格式如下：

```javascript
window._aa_global_config = {
  fileConvertService: {
    baseUrl: 'https://convert.example.com',
    convertApi: '/api/convert',
  },
  workspaceSettings: {
    apiExtensions: 'https://api.example.com/embed/api-extensions',
    dataSources: 'https://api.example.com/embed/data-sources',
    // ...
  },
  loginRedirectUrl: '/dashboard',
};

window._server_config = {
  apiBaseUrl: 'https://api.example.com',
  environment: 'production',
};
```

### 3. useConfig Hook
提供了 `useConfig` hook 来访问配置：

```tsx
import { useConfig, getConfig } from '@/hooks/useConfig';

function MyComponent() {
  const { globalConfig, serverConfig } = useConfig();
  
  // 或者直接获取特定配置
  const apiUrl = getConfig('apiBaseUrl', 'http://localhost:3000');
  
  return <div>API URL: {apiUrl}</div>;
}
```

## 使用方法

### 1. 基本用法

```tsx
'use client';

import { useConfig } from '@/hooks/useConfig';

export default function MyComponent() {
  const { globalConfig, serverConfig } = useConfig();
  
  return (
    <div>
      <p>环境: {serverConfig.environment}</p>
      <p>API地址: {serverConfig.apiBaseUrl}</p>
    </div>
  );
}
```

### 2. 获取特定配置

```tsx
import { getConfig } from '@/hooks/useConfig';

// 在组件中使用
const redirectUrl = getConfig('loginRedirectUrl', '/apps');

// 在函数中使用
function handleLogin() {
  const redirectUrl = getConfig('loginRedirectUrl', '/apps');
  window.location.href = redirectUrl;
}
```

### 3. 检查配置加载状态

```tsx
import { isConfigLoaded } from '@/hooks/useConfig';

function MyComponent() {
  const [configReady, setConfigReady] = useState(false);
  
  useEffect(() => {
    const checkConfig = () => {
      if (isConfigLoaded()) {
        setConfigReady(true);
      }
    };
    
    const timer = setInterval(checkConfig, 100);
    return () => clearInterval(timer);
  }, []);
  
  if (!configReady) {
    return <div>配置加载中...</div>;
  }
  
  return <div>配置已加载</div>;
}
```

## 配置类型定义

```typescript
interface GlobalConfig {
  fileConvertService?: {
    baseUrl: string;
    convertApi: string;
  };
  workspaceSettings?: {
    apiExtensions: string;
    dataSources: string;
    modelProviders: string;
    modalLanguage: string;
    modalUserinfo: string;
  };
  loginRedirectUrl?: string;
  [key: string]: any;
}

interface ServerConfig {
  [key: string]: any;
}
```

## 部署注意事项

1. **配置文件路径**: 确保 `public/static-config/config.js` 文件在部署时可访问
2. **缓存控制**: 脚本URL中添加了随机参数来避免缓存问题
3. **环境区分**: 可以为不同环境准备不同的配置文件

## 与Vue项目的对比

| 特性 | Vue项目 | Next.js项目 |
|------|---------|-------------|
| 脚本注入 | index.html | layout.tsx + Script组件 |
| 配置访问 | useConfig() | useConfig() hook |
| 类型安全 | 部分支持 | 完整TypeScript支持 |
| 热重载 | 支持 | 支持 |

## 测试

访问 `/config-test` 页面可以测试配置加载功能，包括：
- 配置加载状态检查
- 配置内容展示
- 实际使用示例

## 故障排除

1. **配置未加载**: 检查浏览器控制台是否有脚本加载错误
2. **配置不生效**: 确认配置文件语法正确，window对象正确挂载
3. **类型错误**: 检查TypeScript类型定义是否匹配实际配置结构
