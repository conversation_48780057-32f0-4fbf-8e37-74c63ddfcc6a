'use client';

import { useConfig, getConfig } from '@/hooks/useConfig';

export default function ConfigExample() {
  const { globalConfig, serverConfig } = useConfig();

  // 示例：根据配置构建API URL
  const baseUrl = getConfig('apiBaseUrl', 'http://localhost:3000');
  const apiPath = '/api/v1/users';
  const apiUrl = `${baseUrl}${apiPath}`;

  const handleRedirect = () => {
    // 示例：使用配置中的重定向URL
    const redirectUrl = getConfig('loginRedirectUrl', '/apps');
    window.location.href = redirectUrl;
  };

  const handleFileConvert = () => {
    // 示例：使用文件转换服务配置
    const convertService = globalConfig.fileConvertService;
    if (convertService) {
      const convertUrl = `${convertService.baseUrl}${convertService.convertApi}`;
      console.log('文件转换服务URL:', convertUrl);
      // 这里可以调用实际的文件转换API
    }
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <h2 className="text-xl font-semibold mb-4">配置使用示例</h2>

      <div className="space-y-4">
        <div className="p-4 bg-blue-50 rounded-lg">
          <h3 className="font-medium mb-2">API配置示例</h3>
          <p className="text-sm text-gray-600 mb-2">
            当前API URL: <code className="bg-white px-2 py-1 rounded">{apiUrl}</code>
          </p>
          <p className="text-sm text-gray-600">
            环境: <span className="font-medium">{getConfig('environment', '开发环境')}</span>
          </p>
        </div>

        <div className="p-4 bg-green-50 rounded-lg">
          <h3 className="font-medium mb-2">重定向配置示例</h3>
          <p className="text-sm text-gray-600 mb-3">
            登录后重定向到:{' '}
            <code className="bg-white px-2 py-1 rounded">
              {getConfig('loginRedirectUrl', '/apps')}
            </code>
          </p>
          <button
            onClick={handleRedirect}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
          >
            测试重定向
          </button>
        </div>

        <div className="p-4 bg-yellow-50 rounded-lg">
          <h3 className="font-medium mb-2">文件转换服务配置示例</h3>
          {globalConfig.fileConvertService ? (
            <div className="space-y-2">
              <p className="text-sm text-gray-600">
                服务地址:{' '}
                <code className="bg-white px-2 py-1 rounded">
                  {globalConfig.fileConvertService.baseUrl}
                </code>
              </p>
              <p className="text-sm text-gray-600">
                转换接口:{' '}
                <code className="bg-white px-2 py-1 rounded">
                  {globalConfig.fileConvertService.convertApi}
                </code>
              </p>
              <button
                onClick={handleFileConvert}
                className="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
              >
                测试文件转换配置
              </button>
            </div>
          ) : (
            <p className="text-sm text-gray-500">文件转换服务配置未找到</p>
          )}
        </div>

        <div className="p-4 bg-purple-50 rounded-lg">
          <h3 className="font-medium mb-2">工作台设置配置示例</h3>
          {globalConfig.workspaceSettings ? (
            <div className="space-y-1 text-sm">
              <p>
                API扩展:{' '}
                <code className="bg-white px-1 rounded text-xs">
                  {globalConfig.workspaceSettings.apiExtensions}
                </code>
              </p>
              <p>
                数据源:{' '}
                <code className="bg-white px-1 rounded text-xs">
                  {globalConfig.workspaceSettings.dataSources}
                </code>
              </p>
              <p>
                模型提供商:{' '}
                <code className="bg-white px-1 rounded text-xs">
                  {globalConfig.workspaceSettings.modelProviders}
                </code>
              </p>
            </div>
          ) : (
            <p className="text-sm text-gray-500">工作台设置配置未找到</p>
          )}
        </div>

        <div className="p-4 bg-gray-50 rounded-lg">
          <h3 className="font-medium mb-2">自定义配置示例</h3>
          <p className="text-sm text-gray-600">
            测试值: <span className="font-medium">{getConfig('test', '未设置')}</span>
          </p>
        </div>
      </div>
    </div>
  );
}
