import { useEffect, useState } from 'react';

// 定义配置类型
interface GlobalConfig {
  // 文件转换服务配置
  fileConvertService?: {
    baseUrl: string;
    convertApi: string;
  };
  // 工作台设置
  workspaceSettings?: {
    apiExtensions: string;
    dataSources: string;
    modelProviders: string;
    modalLanguage: string;
    modalUserinfo: string;
  };
  // 登录成功后的重定向URL
  loginRedirectUrl?: string;
  // 其他动态配置
  [key: string]: any;
}

interface ServerConfig {
  [key: string]: any;
}

// 扩展Window接口
declare global {
  interface Window {
    _aa_global_config?: GlobalConfig;
    _server_config?: ServerConfig;
  }
}

export function useConfig() {
  const [globalConfig, setGlobalConfig] = useState<GlobalConfig>({
    // 默认配置
    fileConvertService: {
      baseUrl: 'http://*************:8012',
      convertApi: '/getConvertedFile',
    },
    workspaceSettings: {
      apiExtensions: 'http://localhost:3000/embed/api-extensions',
      dataSources: 'http://localhost:3000/embed/data-sources',
      modelProviders: 'http://localhost:3000/embed/model-providers',
      modalLanguage: 'http://localhost:3000/embed/modal-language',
      modalUserinfo: 'http://localhost:3000/embed/modal-userinfo',
    },
    loginRedirectUrl: '/explore/apps',
  });

  const [serverConfig, setServerConfig] = useState<ServerConfig>({});

  useEffect(() => {
    // 检查并合并全局配置
    if (typeof window !== 'undefined') {
      if (!window._aa_global_config) {
        console.warn(
          '请注意，当前未正确挂载 _aa_global_config，请检查部署环境是否正确部署了 config.js'
        );
        window._aa_global_config = {};
      }

      if (!window._server_config) {
        console.warn(
          '请注意，当前未正确挂载 _server_config，请检查部署环境是否正确部署了 config.js'
        );
        window._server_config = {};
      }

      // 合并配置
      setGlobalConfig(prev => ({
        ...prev,
        ...window._aa_global_config,
      }));

      setServerConfig(prev => ({
        ...prev,
        ...window._server_config,
      }));
    }
  }, []);

  return {
    globalConfig,
    serverConfig,
  };
}

// 导出一个获取特定配置的工具函数
export function getConfig(key: string, defaultValue?: any) {
  if (typeof window === 'undefined') {
    return defaultValue;
  }

  const globalConfig = window._aa_global_config || {};
  const serverConfig = window._server_config || {};

  // 先从全局配置中查找，再从服务器配置中查找
  return globalConfig[key] ?? serverConfig[key] ?? defaultValue;
}

// 导出一个检查配置是否已加载的函数
export function isConfigLoaded(): boolean {
  if (typeof window === 'undefined') {
    return false;
  }
  
  return !!(window._aa_global_config || window._server_config);
}
